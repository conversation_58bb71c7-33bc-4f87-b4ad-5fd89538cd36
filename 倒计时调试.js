// 倒计时刷新调试脚本
// 在浏览器控制台中运行此脚本来调试倒计时刷新功能

console.log('🔧 启动倒计时刷新调试模式...');

// 监控变量
let debugInterval = null;
let lastDeviceTime = '';
let lastProductionTime = '';
let countdownHistory = [];

// 调试函数
function debugCountdown() {
    const deviceEl = document.getElementById('device-update-time');
    const productionEl = document.getElementById('production-update-time');
    
    if (!deviceEl || !productionEl) {
        console.log('❌ 找不到倒计时元素');
        return;
    }
    
    const currentDeviceTime = deviceEl.textContent;
    const currentProductionTime = productionEl.textContent;
    const timestamp = new Date().toLocaleTimeString();
    
    // 记录倒计时变化
    if (currentDeviceTime !== lastDeviceTime || currentProductionTime !== lastProductionTime) {
        const entry = {
            time: timestamp,
            device: `${lastDeviceTime} → ${currentDeviceTime}`,
            production: `${lastProductionTime} → ${currentProductionTime}`,
            deviceEnded: currentDeviceTime === '0:00',
            productionEnded: currentProductionTime === '0:00'
        };
        
        countdownHistory.push(entry);
        
        console.log(`⏰ [${timestamp}] 倒计时变化:`);
        console.log(`   设备状态: ${entry.device}`);
        console.log(`   生产数据: ${entry.production}`);
        
        if (entry.deviceEnded || entry.productionEnded) {
            console.log(`🚨 倒计时结束检测: 设备=${entry.deviceEnded}, 生产=${entry.productionEnded}`);
            
            // 检查是否会触发刷新
            const shouldTrigger = (entry.deviceEnded && lastDeviceTime !== '0:00') || 
                                (entry.productionEnded && lastProductionTime !== '0:00');
            console.log(`🔍 应该触发刷新: ${shouldTrigger}`);
            
            if (shouldTrigger) {
                console.log('🎯 预期会触发数据刷新，请观察控制台是否出现刷新日志');
            }
        }
        
        lastDeviceTime = currentDeviceTime;
        lastProductionTime = currentProductionTime;
        
        // 保持历史记录在合理范围内
        if (countdownHistory.length > 20) {
            countdownHistory.shift();
        }
    }
}

// 手动触发倒计时结束测试
function triggerCountdownTest(type = 'device') {
    const elementId = type === 'device' ? 'device-update-time' : 'production-update-time';
    const element = document.getElementById(elementId);
    
    if (!element) {
        console.log(`❌ 找不到${type}倒计时元素`);
        return;
    }
    
    const originalValue = element.textContent;
    console.log(`🧪 手动触发${type}倒计时结束测试`);
    console.log(`   原始值: ${originalValue}`);
    
    // 设置为0:00
    element.textContent = '0:00';
    console.log(`   设置为: 0:00`);
    
    // 等待一秒让updateRealTimeIndicator检测到变化
    setTimeout(() => {
        console.log(`🔄 恢复原始值: ${originalValue}`);
        element.textContent = originalValue;
    }, 2000);
}

// 检查关键函数和对象
function checkSystemStatus() {
    console.log('\n🔍 系统状态检查:');
    
    // 检查关键函数
    const functions = ['updateRealTimeIndicator', 'loadAllData'];
    functions.forEach(funcName => {
        const exists = typeof window[funcName] === 'function';
        console.log(`   ${funcName}: ${exists ? '✅' : '❌'}`);
    });
    
    // 检查数据更新管理器
    const manager = window.dataUpdateManager;
    if (manager) {
        console.log('   dataUpdateManager: ✅');
        console.log(`     - isPaused: ${manager.isPaused}`);
        console.log(`     - isUpdating: ${manager.isUpdating}`);
        console.log(`     - shouldUpdate(): ${manager.shouldUpdate()}`);
    } else {
        console.log('   dataUpdateManager: ❌');
    }
    
    // 检查DOM元素
    const elements = ['device-update-time', 'production-update-time'];
    elements.forEach(id => {
        const el = document.getElementById(id);
        console.log(`   ${id}: ${el ? '✅ ' + el.textContent : '❌'}`);
    });
}

// 显示倒计时历史
function showHistory() {
    console.log('\n📊 倒计时变化历史:');
    if (countdownHistory.length === 0) {
        console.log('   暂无记录');
        return;
    }
    
    countdownHistory.forEach((entry, index) => {
        console.log(`   ${index + 1}. [${entry.time}]`);
        console.log(`      设备: ${entry.device}`);
        console.log(`      生产: ${entry.production}`);
        if (entry.deviceEnded || entry.productionEnded) {
            console.log(`      🚨 倒计时结束!`);
        }
    });
}

// 启动调试监控
function startDebug() {
    if (debugInterval) {
        console.log('⚠️ 调试监控已在运行');
        return;
    }
    
    console.log('🚀 启动倒计时调试监控 (每秒检查一次)');
    
    // 初始化
    const deviceEl = document.getElementById('device-update-time');
    const productionEl = document.getElementById('production-update-time');
    
    if (deviceEl && productionEl) {
        lastDeviceTime = deviceEl.textContent;
        lastProductionTime = productionEl.textContent;
        console.log(`📍 初始倒计时: 设备=${lastDeviceTime}, 生产=${lastProductionTime}`);
    }
    
    debugInterval = setInterval(debugCountdown, 1000);
}

// 停止调试监控
function stopDebug() {
    if (debugInterval) {
        clearInterval(debugInterval);
        debugInterval = null;
        console.log('⏹️ 倒计时调试监控已停止');
    } else {
        console.log('⚠️ 调试监控未在运行');
    }
}

// 导出调试函数到全局
window.debugCountdown = {
    start: startDebug,
    stop: stopDebug,
    test: triggerCountdownTest,
    status: checkSystemStatus,
    history: showHistory
};

// 自动启动
checkSystemStatus();
startDebug();

console.log('\n💡 调试命令:');
console.log('   debugCountdown.start()  - 启动监控');
console.log('   debugCountdown.stop()   - 停止监控');
console.log('   debugCountdown.test()   - 手动测试 (默认设备)');
console.log('   debugCountdown.test("production") - 测试生产数据');
console.log('   debugCountdown.status() - 检查系统状态');
console.log('   debugCountdown.history() - 显示变化历史');
console.log('\n🎯 现在等待倒计时自然到达0:00，或使用 debugCountdown.test() 手动测试');
