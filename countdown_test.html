<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倒计时刷新功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a202c;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(45, 55, 72, 0.8);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #4fd1c7;
        }
        .countdown-display {
            background: rgba(79, 209, 199, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #4fd1c7;
        }
        .status-log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #4fd1c7;
            padding-left: 10px;
        }
        .log-entry.refresh {
            border-left-color: #48bb78;
            background: rgba(72, 187, 120, 0.1);
        }
        .log-entry.countdown {
            border-left-color: #ed8936;
            background: rgba(237, 137, 54, 0.1);
        }
        .test-controls {
            margin: 20px 0;
        }
        button {
            background: #4fd1c7;
            color: #1a202c;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        button:hover {
            background: #38b2ac;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 倒计时数据刷新功能测试</h1>
        
        <div class="countdown-display">
            <h3>📊 实时倒计时显示</h3>
            <div style="display: flex; gap: 30px; align-items: center;">
                <div>
                    <span>设备状态倒计时: </span>
                    <span id="device-countdown" style="color: #4fd1c7; font-weight: bold; font-size: 18px;">--:--</span>
                </div>
                <div>
                    <span>生产数据倒计时: </span>
                    <span id="production-countdown" style="color: #4fd1c7; font-weight: bold; font-size: 18px;">--:--</span>
                </div>
                <div>
                    <span>系统状态: </span>
                    <span id="system-status" style="color: #48bb78; font-weight: bold;">正常</span>
                </div>
            </div>
        </div>

        <div class="test-controls">
            <h3>🧪 测试控制</h3>
            <button onclick="simulateCountdownEnd('device')">模拟设备状态倒计时结束</button>
            <button onclick="simulateCountdownEnd('production')">模拟生产数据倒计时结束</button>
            <button onclick="clearLog()">清空日志</button>
            <button onclick="toggleAutoTest()">开启/关闭自动测试</button>
        </div>

        <div class="status-log">
            <h3>📝 操作日志</h3>
            <div id="log-container">
                <div class="log-entry">测试页面已加载，等待倒计时事件...</div>
            </div>
        </div>
    </div>

    <script>
        let autoTestInterval = null;
        let testCounter = 0;

        // 模拟倒计时更新
        function updateCountdowns() {
            const now = new Date();
            const totalSeconds = now.getMinutes() * 60 + now.getSeconds();
            
            // 计算到下一分钟的倒计时
            const nextUpdate = 60 - (totalSeconds % 60);
            const minutes = Math.floor(nextUpdate / 60);
            const seconds = nextUpdate % 60;
            
            const countdownText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            
            const deviceEl = document.getElementById('device-countdown');
            const productionEl = document.getElementById('production-countdown');
            
            if (deviceEl) deviceEl.textContent = countdownText;
            if (productionEl) productionEl.textContent = countdownText;
            
            // 检测倒计时结束
            if (countdownText === '0:00') {
                logEvent('countdown', `倒计时结束: ${countdownText} - 触发数据刷新`);
                simulateDataRefresh('自动');
            }
        }

        // 模拟倒计时结束
        function simulateCountdownEnd(type) {
            const elementId = type === 'device' ? 'device-countdown' : 'production-countdown';
            const element = document.getElementById(elementId);
            
            if (element) {
                element.textContent = '0:00';
                logEvent('countdown', `手动触发${type === 'device' ? '设备状态' : '生产数据'}倒计时结束`);
                simulateDataRefresh('手动');
                
                // 2秒后恢复正常倒计时
                setTimeout(() => {
                    updateCountdowns();
                }, 2000);
            }
        }

        // 模拟数据刷新
        function simulateDataRefresh(trigger) {
            testCounter++;
            logEvent('refresh', `第${testCounter}次数据刷新 (${trigger}触发) - 模拟加载设备统计、设备列表、最新信息等数据`);
            
            // 模拟加载时间
            setTimeout(() => {
                logEvent('refresh', `第${testCounter}次数据刷新完成 - 所有数据已更新`);
            }, 1000);
        }

        // 记录日志
        function logEvent(type, message) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = '<div class="log-entry">日志已清空</div>';
            testCounter = 0;
        }

        // 切换自动测试
        function toggleAutoTest() {
            if (autoTestInterval) {
                clearInterval(autoTestInterval);
                autoTestInterval = null;
                logEvent('countdown', '自动测试已停止');
            } else {
                autoTestInterval = setInterval(() => {
                    const types = ['device', 'production'];
                    const randomType = types[Math.floor(Math.random() * types.length)];
                    simulateCountdownEnd(randomType);
                }, 10000); // 每10秒触发一次
                logEvent('countdown', '自动测试已开启 (每10秒触发一次)');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            logEvent('countdown', '测试页面初始化完成');
            
            // 每秒更新倒计时
            setInterval(updateCountdowns, 1000);
            updateCountdowns();
            
            logEvent('countdown', '倒计时监控已启动，等待倒计时到达0:00时自动触发刷新');
        });
    </script>
</body>
</html>
