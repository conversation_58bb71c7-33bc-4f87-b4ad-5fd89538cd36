// 倒计时刷新功能验证脚本
// 在浏览器控制台中运行此脚本来验证功能

console.log('🔍 开始验证倒计时刷新功能...');

// 1. 检查关键函数是否存在
const functionsToCheck = [
    'updateRealTimeIndicator',
    'loadAllData',
    'updateDataStatus',
    'initRealTimeIndicator'
];

console.log('\n📋 检查关键函数:');
functionsToCheck.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName} - 存在`);
    } else {
        console.log(`❌ ${funcName} - 不存在`);
    }
});

// 2. 检查DOM元素是否存在
const elementsToCheck = [
    'device-update-time',
    'production-update-time',
    'system-status'
];

console.log('\n🎯 检查DOM元素:');
elementsToCheck.forEach(elementId => {
    const element = document.getElementById(elementId);
    if (element) {
        console.log(`✅ ${elementId} - 存在，当前值: "${element.textContent}"`);
    } else {
        console.log(`❌ ${elementId} - 不存在`);
    }
});

// 3. 检查数据更新管理器
console.log('\n⚙️ 检查数据更新管理器:');
if (window.dataUpdateManager) {
    console.log('✅ dataUpdateManager - 存在');
    console.log(`   - isPaused: ${window.dataUpdateManager.isPaused}`);
    console.log(`   - isUpdating: ${window.dataUpdateManager.isUpdating}`);
    console.log(`   - shouldUpdate(): ${window.dataUpdateManager.shouldUpdate()}`);
} else {
    console.log('❌ dataUpdateManager - 不存在');
}

// 4. 模拟倒计时结束测试
console.log('\n🧪 模拟倒计时结束测试:');

function simulateCountdownEnd() {
    const deviceEl = document.getElementById('device-update-time');
    const productionEl = document.getElementById('production-update-time');
    
    if (deviceEl && productionEl) {
        console.log('📊 模拟设备状态倒计时结束...');
        
        // 保存原始值
        const originalDeviceTime = deviceEl.textContent;
        const originalProductionTime = productionEl.textContent;
        
        // 设置为0:00触发刷新
        deviceEl.textContent = '0:00';
        
        // 手动调用更新函数
        if (typeof updateRealTimeIndicator === 'function') {
            updateRealTimeIndicator();
            console.log('✅ updateRealTimeIndicator 已调用');
        }
        
        // 恢复原始值
        setTimeout(() => {
            deviceEl.textContent = originalDeviceTime;
            productionEl.textContent = originalProductionTime;
            console.log('🔄 倒计时值已恢复');
        }, 3000);
        
    } else {
        console.log('❌ 无法找到倒计时元素');
    }
}

// 5. 检查loadAllData函数的静默模式
console.log('\n🔇 测试静默模式:');
if (typeof loadAllData === 'function') {
    console.log('测试 loadAllData(true) - 静默模式');
    // 注意：这里只是检查函数调用，实际的网络请求可能会失败
    try {
        // loadAllData(true); // 取消注释以实际测试
        console.log('✅ loadAllData 支持静默模式参数');
    } catch (error) {
        console.log('❌ loadAllData 静默模式测试失败:', error);
    }
} else {
    console.log('❌ loadAllData 函数不存在');
}

// 6. 提供手动测试函数
window.testCountdownRefresh = simulateCountdownEnd;

console.log('\n🎉 验证完成！');
console.log('💡 提示: 运行 testCountdownRefresh() 来手动测试倒计时刷新功能');
console.log('💡 提示: 观察控制台日志中的 "📊 倒计时结束，触发静默数据刷新..." 消息');

// 7. 监听实际的倒计时变化
let lastDeviceTime = '';
let lastProductionTime = '';

function monitorCountdown() {
    const deviceEl = document.getElementById('device-update-time');
    const productionEl = document.getElementById('production-update-time');
    
    if (deviceEl && productionEl) {
        const currentDeviceTime = deviceEl.textContent;
        const currentProductionTime = productionEl.textContent;
        
        // 检测倒计时变化
        if (currentDeviceTime !== lastDeviceTime) {
            console.log(`⏰ 设备状态倒计时: ${lastDeviceTime} → ${currentDeviceTime}`);
            if (currentDeviceTime === '0:00') {
                console.log('🚨 设备状态倒计时结束！应该触发数据刷新');
            }
            lastDeviceTime = currentDeviceTime;
        }
        
        if (currentProductionTime !== lastProductionTime) {
            console.log(`⏰ 生产数据倒计时: ${lastProductionTime} → ${currentProductionTime}`);
            if (currentProductionTime === '0:00') {
                console.log('🚨 生产数据倒计时结束！应该触发数据刷新');
            }
            lastProductionTime = currentProductionTime;
        }
    }
}

// 每秒监控倒计时变化
setInterval(monitorCountdown, 1000);
console.log('👁️ 倒计时监控已启动，将在控制台显示倒计时变化');
