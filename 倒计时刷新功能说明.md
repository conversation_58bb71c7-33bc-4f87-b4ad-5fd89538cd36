# 倒计时数据刷新功能说明

## 功能概述

在左下角的实时数据监控区域，显示了设备状态和生产数据的倒计时。当倒计时到达 0:00 时，系统会自动触发数据刷新，确保界面显示的数据始终是最新的。

## 实现细节

### 1. 倒计时显示
- **位置**: 页面左下角的实时数据监控区域
- **显示内容**: 
  - 设备状态倒计时 (每1分钟更新)
  - 生产数据倒计时 (每1分钟更新)
  - 系统状态指示

### 2. 倒计时逻辑
```javascript
// 计算到下一分钟的倒计时
const totalSeconds = now.getMinutes() * 60 + now.getSeconds();
const nextUpdate = 60 - (totalSeconds % 60);
const minutes = Math.floor(nextUpdate / 60);
const seconds = nextUpdate % 60;
```

### 3. 自动刷新触发
当倒计时显示为 "0:00" 时，系统会：
1. 检测倒计时结束事件
2. 验证数据更新管理器状态，避免重复刷新
3. 触发静默数据刷新
4. 更新状态指示器

### 4. 刷新的数据内容
倒计时触发的数据刷新包括：
- **关键数据** (顺序加载):
  - 设备统计信息
  - 设备列表数据
- **次要数据** (并行加载):
  - 最新信息
- **后台数据** (异步加载):
  - 日历异常数据
  - 图表数据

## 代码实现

### 主要函数

#### 1. `updateRealTimeIndicator()`
- 每秒执行一次
- 更新倒计时显示
- 检测倒计时结束并触发刷新

#### 2. `loadAllData(silent = false)`
- 加载所有数据的主函数
- 支持静默模式，倒计时触发时不显示加载状态
- 分批加载数据以提高性能

#### 3. 倒计时结束检测逻辑
```javascript
// 检查设备状态倒计时是否结束
const deviceCountdownEnded = currentDeviceTime === '0:00' || 
    (prevDeviceTime && prevDeviceTime !== '0:00' && currentDeviceTime === '0:00');

// 检查生产数据倒计时是否结束
const productionCountdownEnded = currentProductionTime === '0:00' || 
    (prevProductionTime && prevProductionTime !== '0:00' && currentProductionTime === '0:00');
```

## 优化特性

### 1. 静默刷新
- 倒计时触发的刷新使用静默模式
- 不显示加载指示器，避免干扰用户
- 刷新完成后显示简短的成功提示

### 2. 冲突避免
- 检查数据更新管理器状态
- 如果正在进行其他更新，跳过倒计时刷新
- 避免重复的网络请求

### 3. 错误处理
- 静默模式下不显示错误弹窗
- 仅在控制台记录错误信息
- 保持用户界面的稳定性

### 4. 性能优化
- 分批加载数据
- 关键数据优先加载
- 后台数据异步处理

## 测试方法

### 1. 自然测试
等待倒计时自然到达 0:00，观察是否触发数据刷新

### 2. 手动测试
使用提供的测试页面 `countdown_test.html`：
- 模拟倒计时结束
- 查看刷新日志
- 验证功能正常

### 3. 控制台监控
在浏览器控制台中查看相关日志：
```
📊 设备状态倒计时结束，触发静默数据刷新...
📊 设备状态倒计时触发的静默数据刷新完成
```

## 配置参数

- **倒计时间隔**: 1分钟 (60秒)
- **刷新延迟**: 1秒 (状态提示延迟)
- **静默模式**: 默认开启 (倒计时触发时)

## 注意事项

1. 倒计时刷新与定时刷新是独立的，不会相互冲突
2. 页面不可见时，倒计时仍会继续，但数据更新会暂停
3. 网络异常时，静默刷新失败不会显示错误弹窗
4. 倒计时显示基于本地时间，确保时间同步的准确性

## 相关文件

- `script.js`: 主要实现代码
- `countdown_test.html`: 功能测试页面
- `index.html`: 主页面
- `style.css`: 样式定义
