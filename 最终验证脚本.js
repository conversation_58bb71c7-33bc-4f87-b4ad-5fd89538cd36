// 最终验证脚本 - 在主页面控制台中运行
// 用于验证倒计时刷新功能是否正常工作

console.log('🔍 开始最终验证...');

// 1. 验证倒计时计算逻辑
function testCountdownLogic() {
    console.log('\n📊 测试倒计时计算逻辑:');
    
    // 模拟不同的秒数
    const testCases = [0, 1, 30, 59];
    
    testCases.forEach(seconds => {
        const nextUpdate = seconds === 0 ? 0 : 60 - seconds;
        const minutes = Math.floor(nextUpdate / 60);
        const displaySeconds = nextUpdate % 60;
        const result = `${minutes}:${displaySeconds.toString().padStart(2, '0')}`;
        
        console.log(`   秒数 ${seconds.toString().padStart(2)} → 倒计时 ${result} ${result === '0:00' ? '🚨' : ''}`);
    });
}

// 2. 检查当前倒计时状态
function checkCurrentCountdown() {
    console.log('\n⏰ 当前倒计时状态:');
    
    const deviceEl = document.getElementById('device-update-time');
    const productionEl = document.getElementById('production-update-time');
    
    if (deviceEl && productionEl) {
        const now = new Date();
        const currentSeconds = now.getSeconds();
        const expectedCountdown = currentSeconds === 0 ? '0:00' : `0:${(60 - currentSeconds).toString().padStart(2, '0')}`;
        
        console.log(`   当前时间: ${now.toLocaleTimeString()}`);
        console.log(`   当前秒数: ${currentSeconds}`);
        console.log(`   预期倒计时: ${expectedCountdown}`);
        console.log(`   设备倒计时: ${deviceEl.textContent}`);
        console.log(`   生产倒计时: ${productionEl.textContent}`);
        console.log(`   是否匹配: ${deviceEl.textContent === expectedCountdown ? '✅' : '❌'}`);
    } else {
        console.log('   ❌ 找不到倒计时元素');
    }
}

// 3. 模拟倒计时结束并验证刷新
function simulateAndVerify() {
    console.log('\n🧪 模拟倒计时结束测试:');
    
    const deviceEl = document.getElementById('device-update-time');
    if (!deviceEl) {
        console.log('   ❌ 找不到设备倒计时元素');
        return;
    }
    
    const originalValue = deviceEl.textContent;
    console.log(`   原始倒计时: ${originalValue}`);
    
    // 设置为非0:00值
    deviceEl.textContent = '0:01';
    console.log(`   设置为: 0:01`);
    
    // 等待1秒后设置为0:00
    setTimeout(() => {
        console.log('   设置为: 0:00 (应该触发刷新)');
        deviceEl.textContent = '0:00';
        
        // 手动调用更新函数来触发检测
        if (typeof updateRealTimeIndicator === 'function') {
            updateRealTimeIndicator();
        }
        
        // 恢复原始值
        setTimeout(() => {
            deviceEl.textContent = originalValue;
            console.log(`   恢复为: ${originalValue}`);
        }, 3000);
    }, 1000);
}

// 4. 监控真实的倒计时变化
let realTimeMonitor = null;
let lastDeviceTime = '';

function startRealTimeMonitor() {
    console.log('\n👁️ 启动实时监控 (30秒):');
    
    const deviceEl = document.getElementById('device-update-time');
    if (!deviceEl) {
        console.log('   ❌ 找不到倒计时元素');
        return;
    }
    
    lastDeviceTime = deviceEl.textContent;
    console.log(`   初始倒计时: ${lastDeviceTime}`);
    
    let monitorCount = 0;
    realTimeMonitor = setInterval(() => {
        const currentTime = deviceEl.textContent;
        const now = new Date();
        
        if (currentTime !== lastDeviceTime) {
            console.log(`   [${now.toLocaleTimeString()}] ${lastDeviceTime} → ${currentTime}`);
            
            if (currentTime === '0:00') {
                console.log('   🚨 检测到0:00! 应该触发数据刷新');
            }
            
            lastDeviceTime = currentTime;
        }
        
        monitorCount++;
        if (monitorCount >= 30) {
            clearInterval(realTimeMonitor);
            console.log('   ⏹️ 监控结束');
        }
    }, 1000);
}

// 5. 检查系统状态
function checkSystemStatus() {
    console.log('\n⚙️ 系统状态检查:');
    
    // 检查关键函数
    const updateFunc = typeof updateRealTimeIndicator === 'function';
    const loadFunc = typeof loadAllData === 'function';
    console.log(`   updateRealTimeIndicator: ${updateFunc ? '✅' : '❌'}`);
    console.log(`   loadAllData: ${loadFunc ? '✅' : '❌'}`);
    
    // 检查数据管理器
    const manager = window.dataUpdateManager;
    if (manager) {
        console.log(`   dataUpdateManager: ✅`);
        console.log(`     - shouldUpdate(): ${manager.shouldUpdate()}`);
    } else {
        console.log(`   dataUpdateManager: ❌`);
    }
}

// 执行所有测试
testCountdownLogic();
checkCurrentCountdown();
checkSystemStatus();

console.log('\n🎯 测试选项:');
console.log('   simulateAndVerify() - 模拟倒计时结束测试');
console.log('   startRealTimeMonitor() - 启动30秒实时监控');
console.log('   checkCurrentCountdown() - 检查当前倒计时状态');

// 导出测试函数
window.countdownVerify = {
    simulate: simulateAndVerify,
    monitor: startRealTimeMonitor,
    check: checkCurrentCountdown,
    logic: testCountdownLogic
};

console.log('\n💡 使用方法:');
console.log('   countdownVerify.simulate() - 模拟测试');
console.log('   countdownVerify.monitor() - 实时监控');
console.log('   countdownVerify.check() - 状态检查');

// 如果当前秒数接近0，提醒用户观察
const currentSeconds = new Date().getSeconds();
if (currentSeconds >= 55 || currentSeconds <= 5) {
    console.log('\n⚠️ 当前时间接近整分钟，请观察倒计时是否会显示0:00并触发刷新!');
}
