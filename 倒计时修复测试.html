<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倒计时修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a202c;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(45, 55, 72, 0.8);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #4fd1c7;
        }
        .countdown-display {
            background: rgba(79, 209, 199, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #4fd1c7;
            text-align: center;
        }
        .countdown-value {
            font-size: 48px;
            font-weight: bold;
            color: #4fd1c7;
            margin: 10px 0;
        }
        .info {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .zero-highlight {
            background: rgba(255, 0, 0, 0.2);
            border: 2px solid #ff6b6b;
        }
        button {
            background: #4fd1c7;
            color: #1a202c;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        button:hover {
            background: #38b2ac;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 倒计时修复测试</h1>
        
        <div class="countdown-display" id="countdown-container">
            <h3>📊 修复后的倒计时逻辑</h3>
            <div class="countdown-value" id="countdown-display">--:--</div>
            <div>到下一分钟的倒计时</div>
        </div>

        <div class="info">
            <h3>📝 实时信息</h3>
            <div>当前时间: <span id="current-time">--:--:--</span></div>
            <div>当前秒数: <span id="current-seconds">--</span></div>
            <div>计算结果: <span id="calculation">--</span></div>
            <div>倒计时值: <span id="countdown-value">--:--</span></div>
            <div>是否为0:00: <span id="is-zero">否</span></div>
        </div>

        <div style="text-align: center;">
            <button onclick="toggleMonitoring()">开始/停止监控</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="info">
            <h3>📋 0:00 检测日志</h3>
            <div id="log-container">等待检测...</div>
        </div>
    </div>

    <script>
        let monitoringInterval = null;
        let logContainer = document.getElementById('log-container');
        let lastCountdown = '';

        function updateCountdown() {
            const now = new Date();
            const currentSeconds = now.getSeconds();
            
            // 使用修复后的逻辑
            const nextUpdate = currentSeconds === 0 ? 0 : 60 - currentSeconds;
            const minutes = Math.floor(nextUpdate / 60);
            const seconds = nextUpdate % 60;
            const countdownText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            
            // 更新显示
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
            document.getElementById('current-seconds').textContent = currentSeconds;
            document.getElementById('calculation').textContent = `${currentSeconds} === 0 ? 0 : 60 - ${currentSeconds} = ${nextUpdate}`;
            document.getElementById('countdown-value').textContent = countdownText;
            document.getElementById('countdown-display').textContent = countdownText;
            
            // 检查是否为0:00
            const isZero = countdownText === '0:00';
            document.getElementById('is-zero').textContent = isZero ? '是' : '否';
            
            // 高亮显示0:00
            const container = document.getElementById('countdown-container');
            if (isZero) {
                container.classList.add('zero-highlight');
            } else {
                container.classList.remove('zero-highlight');
            }
            
            // 检测变化并记录0:00
            if (countdownText !== lastCountdown) {
                if (isZero) {
                    logZeroDetection(now.toLocaleTimeString(), lastCountdown, countdownText);
                }
                lastCountdown = countdownText;
            }
        }

        function logZeroDetection(time, from, to) {
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                margin: 5px 0;
                padding: 10px;
                background: rgba(255, 107, 107, 0.2);
                border: 1px solid #ff6b6b;
                border-radius: 5px;
                color: #ff6b6b;
                font-weight: bold;
            `;
            logEntry.innerHTML = `🚨 [${time}] 检测到0:00! ${from} → ${to}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`🚨 倒计时到达0:00! 时间: ${time}`);
        }

        function toggleMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                console.log('⏹️ 监控已停止');
            } else {
                monitoringInterval = setInterval(updateCountdown, 1000);
                updateCountdown(); // 立即更新一次
                console.log('▶️ 监控已开始');
            }
        }

        function clearLog() {
            logContainer.innerHTML = '等待检测...';
        }

        // 页面加载后自动开始监控
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 倒计时修复测试页面已加载');
            toggleMonitoring();
        });
    </script>
</body>
</html>
